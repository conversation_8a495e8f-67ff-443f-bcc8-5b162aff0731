import asyncio
import httpx
import json


URL = "https://open.bigmodel.cn/api/paas/v4/web_search"
APIKEY = "06708a25c8ba4631a71d016b483d7475.uY5AHsxOsriZz8Ub"

async def web_search():
    async with httpx.AsyncClient(transport=httpx.AsyncHTTPTransport(retries=3)) as client:
        response = await client.post(
            url=URL,
            headers={"Authorization": APIKEY},
            json={
                "search_query": "浦银理财是做什么的",
                "search_engine": "search_pro",
                "search_intent": False,
                "count": 10,
                "content_size": "high",
            }
        )
        print(json.dumps(response.json(), indent=4, ensure_ascii=False))


if __name__ == '__main__':
    asyncio.run(web_search())
