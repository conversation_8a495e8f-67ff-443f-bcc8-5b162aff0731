#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import os
import time
import uuid
import math
import functools
from typing import Optional, Callable, Any, List

import jieba.analyse

from engine.es import es
from common.logger import logger
from controller.retriever.rewrite import RewriteEngine
from controller.engine import Embedding<PERSON>ng<PERSON>, RerankEngine, ChatHistory
from controller.operator.chunking.base import RetrieveChunkModel


BASE_DIR = os.path.dirname(os.path.abspath(__file__))


def get_stopwords():
    stopwords_path = os.path.join(BASE_DIR, "stopwords.txt")
    with open(stopwords_path, mode="r", encoding="utf-8") as file:
        return [line.strip() for line in file.readlines() if line.strip()]


def get_synonyms():
    synonyms_path = os.path.join(BASE_DIR, "synonyms.json")
    with open(synonyms_path, mode="r", encoding="utf-8") as file:
        synonyms = json.load(fp=file)
    return synonyms


def get_vocab():
    vocab_path = os.path.join(BASE_DIR, "vocab.json")
    with open(vocab_path, mode="r", encoding="utf-8") as file:
        vocab = json.load(fp=file)
    return vocab

def async_time_cost(name: Optional[str] = None) -> Callable:
    """装饰器：计算函数执行时间并记录日志

    Args:
        name: 自定义日志名称，如果为None则使用方法名

    Returns:
        装饰器函数
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 使用提供的名称或函数名
            log_name = name or func.__name__
            # 记录开始时间
            start_time = time.time()
            # 执行原函数
            result = await func(*args, **kwargs)
            # 计算耗时
            cost_time = time.time() - start_time
            # 记录日志
            if isinstance(retriever := args[0], BaseRetriever):
                logger.info(f"{retriever.log_prefix}{log_name} 耗时 {cost_time:.3f}s")
            else:
                logger.info(f"{log_name} 耗时 {cost_time:.3f}s")
            return result

        return wrapper

    return decorator


class BaseRetriever:
    """集中存放各个retriever所需的基础资源、策略、引擎管理等"""
    stopwords = get_stopwords()
    synonyms = get_synonyms()
    vocab = get_vocab()

    def __init__(self,
                 request_id: str | None = None,
                 rewrite_engine: RewriteEngine | None = None,
                 embedding_engine: EmbeddingEngine | None = None,
                 rerank_engine: RerankEngine | None = None,
                 topn: int = 20,
                 rerank_max_size: int = 30,
                 rerank_threshold: float = 0.2,
                 log_prefix: str = "",
                 bm25_weight: int |float = 1,
                 embedding_weight: int | float = 1,
                 custom_boost: list[dict] = None):
        """
        Args:
            request_id: 检索请求的唯一标识符，如果为None则自动生成
            custom_boost: 文档召回的boost要求
            bm25_weight: BM25检索结果的权重系数，默认为1
            embedding_weight: 向量检索结果的权重系数，默认为1
            rewrite_engine: 问题改写引擎实例
            embedding_engine: 文本向量化引擎实例
            rerank_engine: 检索结果重排序引擎实例
            rerank_max_size: rerank engine放入的片段数,无rerank_engine则无效
            rerank_threshold: rerank engine的保留阈值,无rerank_engine则无效
            topn: 最终最大片段数量
        """
        # [通用配置]
        self.request_id = request_id or uuid.uuid4().hex
        self.log_prefix = log_prefix
        self.query: str | None = None  # 原始问句,retrieve开始时阶段补足
        self._es_max_score = 3.4028235e+38
        self.custom_boost: list[dict] | None = custom_boost
        self.topn = topn

        # [rewriting相关参数]
        self.rewrite_engine: RewriteEngine | None = rewrite_engine
        self.history: list[ChatHistory] | None = None  #  历史问答,retrieve开始时阶段补足
        self.query_rewrite: str | None = None  # 多轮改写问题,如果有rewrite_engine,在rewriting补足
        self.query_keywords: list[str] | None = None  # 问题关键词,如果有rewrite_engine,在rewriting补足
        self.query_associative_keywords: str | None = None  # 问题联想词,如果有rewrite_engine,在rewriting补足
        self.query_search_terms: str | None = None  # 问题扩展,如果有rewrite_engine,在rewriting补足
        self.query_token_weight: dict | None = None  # 问句关键词及权重,如果有query_rewrite则使用query_rewrite,否则使用query

        # [embedding相关参数]
        self.embedding_engine = embedding_engine
        self.query_vector: list[float] | None = None  # 问句向量,如果有query_rewrite则使用query_rewrite,否则使用query

        # [retrieve相关参数]
        self.bm25_weight = bm25_weight
        self.embedding_weight = embedding_weight if self.embedding_engine else 0

        # 默认文档字段权重
        self.field_boost = {
            "filename": 4,
            "source": 4,
            "author": 2,
            "title": 2,
            "plain_text": 1,
        }

        # 默认分片字段权重
        self.chunk_field_boost = {
            "chunks.title": 2,
            "chunks.plain_content": 1,
        }

        # [rerank相关参数]
        self.rerank_engine = rerank_engine
        self.rerank_max_size = rerank_max_size
        self.rerank_max_tokens = 512
        self.rerank_batch_size = 16
        self.rerank_threshold = rerank_threshold

        # 最终结果
        self.chunks: list[dict] | None = []

    @async_time_cost()
    async def rewriting(self):
        if self.rewrite_engine is None:
            self.query_token_weight = await self.get_query_token_weight()
        else:

            rewrite_res = await self.rewrite_engine.rewrite(query=self.query, history=self.history)
            logger.info(self.log_prefix+f"问题改写: {json.dumps(rewrite_res, ensure_ascii=False)}")

            self.query_rewrite = rewrite_res.get("query_rewrite")
            self.query_keywords = rewrite_res.get("query_keywords")
            self.query_associative_keywords = rewrite_res.get("query_associative_keywords")
            self.query_search_terms = rewrite_res.get("query_search_terms")
            self.query_token_weight = await self.get_query_token_weight_with_rewrite()

    @async_time_cost()
    async def embedding(self):
        if self.embedding_weight > 0 and self.embedding_engine is not None:
            vectors = await self.embedding_engine.encode(text=[self.query_rewrite if self.query_rewrite else self.query])
            self.query_vector = vectors[0]

    async def retrieve(self, query: str, history: list[ChatHistory] = None) -> List[RetrieveChunkModel]: ...

    def coarse_ranking(self, source_chunks: list):
        """由于切片会导致片段中可能丢失上下文的核心词,导致可能无关的某个片段因为获得某个词的累积效应而排在较高位置
        为了解决这个问题,在Python层面做后处理,在粗筛后重新进行排序
        注意: 此方法只依靠idf思想,与原始算法并无关系;重算池子中TOPN相关文档的词频出现率对分数进行提权,仍依赖ES分数;

        rerank是不稳定的方法,只对一些极端情况有正向作用,故生效情况需要较为严格.目前对文档进行分数增强需要经过2个条件: 该词出现的较少/包含该词的文档较少
        """
        if not source_chunks:
            return source_chunks

        # 只取解析出来的词中,
        rerank_tokens = {word: weight for word, weight in self.query_token_weight.items() if len(word) > 1 and weight > 0.5}
        query_keyword_count = {word: 0 for word, weight in rerank_tokens.items()}
        doc_contains_count = {word: 0 for word, weight in rerank_tokens.items()}

        for chunk in source_chunks:
            chunk["judge_text"] = ("-".join(chunk["title"]) + chunk["plain_content"] if chunk["title"] else chunk["plain_content"]).replace("\n", "").replace(" ", "")
            # 加和每个结果的关键词数量
            for kw in query_keyword_count:
                query_keyword_count[kw] += chunk["judge_text"].count(kw)
                doc_contains_count[kw] += 1 if kw in chunk["judge_text"] else 0

        # 总平均词频 = 总词频数 / 词数 + 1
        if not query_keyword_count:
            avg_kw_count = 1
        else:
            avg_kw_count = int(sum(list(query_keyword_count.values())) / len(query_keyword_count) + 1)
        # 为低词频文档加权
        for word, count in query_keyword_count.items():
            if count == 0:
                continue

            # 单个词权重倍率 = 总平均词频 / 无变动倍率 / 单词词频
            weight = avg_kw_count / 1.5 / count
            # 无变动倍率保证单词词频略微超过总平均词频时,不会产生分数变动
            # 包含该词的文档占比 / 召回文档数 >= 0.25时,不会产生分数变动
            if weight <= 1 or doc_contains_count[word] / len(source_chunks) >= 0.25:
                continue

            # 分数放大倍率
            low_freq_max_weight = 3  # 单词最大分数放大率
            ratio = min(1 + math.log(weight), low_freq_max_weight)  # [1, low_freq_max_weight]
            for chunk in source_chunks:
                if word in chunk["judge_text"]:
                    chunk["score"] *= ratio

        for chunk in source_chunks:
            if "judge_text" in chunk:
                del chunk["judge_text"]

        # 返回粗排后topn1.5倍数量的片段
        source_chunks = list(sorted(source_chunks, key=lambda x: x["score"], reverse=True))
        return source_chunks[:self.rerank_max_size if self.rerank_engine else self.topn]

    @async_time_cost()
    async def model_reranking(self, source_chunks: list[dict]):
        if not self.rerank_engine:
            return source_chunks
        if len(source_chunks) == 1:
            return source_chunks

        rerank_text_mapping = {}
        cid_scores = {}
        for chunk in source_chunks:
            filename_title_prefix = f"{chunk['filename']}-{'_'.join(chunk['title'])}:"
            prefix_token_counts = len(filename_title_prefix)
            cid_scores[chunk["cid"]] = []
            if prefix_token_counts + chunk["token_counts"] <= self.rerank_max_tokens:
                rerank_text_mapping[filename_title_prefix+chunk["plain_content"]] = chunk["cid"]
            else:
                for step in range(0, len(chunk["plain_content"]), self.rerank_max_tokens - prefix_token_counts):
                    rerank_text_mapping[filename_title_prefix + chunk["plain_content"][step:step+self.rerank_max_tokens-prefix_token_counts]] = chunk["cid"]

        rerank_texts = list(rerank_text_mapping.keys())
        for step in range(0, len(rerank_texts), self.rerank_batch_size):
            batch_texts = rerank_texts[step:step+self.rerank_batch_size]
            res = await self.rerank_engine.rerank(query=self.query, docs=batch_texts)
            for r in res:
                cid = rerank_text_mapping[batch_texts[r["index"]]]
                cid_scores[cid].append(r["relevance_score"])
        for source_chunk in source_chunks:
            source_chunk["relevance_score"] = max(cid_scores[source_chunk["cid"]]) if cid_scores[source_chunk["cid"]] else 0

        source_chunks = list(sorted(source_chunks, key=lambda x: x["relevance_score"], reverse=True))
        if self.rerank_threshold:
            source_chunks = [chunk for chunk in source_chunks if chunk["relevance_score"] >= self.rerank_threshold]
        return source_chunks

    async def get_query_token_weight(self, threshold: float = 0.1):
        if self.query is None:
            raise ValueError("`get_query_token_weight`需要query参数处理完毕")
        token_weight = {}

        smart_tokens = await self.es_tokens(query=self.query, analyzer="ik_smart")

        # 每有一个重复的词,说明此词可能比较重要,权重加0.3
        for token in smart_tokens:
            if token in token_weight:
                token_weight[token] += 0.3
            else:
                token_weight[token] = 1

        # 使用max_word最细粒度分词
        max_word_tokens = await self.es_tokens(query=self.query, analyzer="ik_max_word")

        for token in max_word_tokens:
            # 如果最细粒度分词在停用词中,或者在词库却属于不利于召回的词性,不加入召回
            if token in self.stopwords:
                continue
            if token in self.vocab:
                # c:连词  p:介词  u:助词  o:拟声词  zg:低价值词?
                if self.vocab[token]["s"] in ("c", "p", "u", "o", "zg"):
                    continue

            # 如果max_word分词的词不在smart的分词中长度大于1,且词被某个已有的词完全包含,则权重等于长词
            if token not in token_weight:
                if len(token) > 1:
                    for tw in token_weight.keys():
                        if token in tw:
                            token_weight[token] = token_weight[tw]
                            break
                    else:
                        token_weight[token] = 0.3
                # 否则这个词仅给与比较低的分数
                else:
                    token_weight[token] = 0.3

        # 使用jieba关键词,如果关键词和已提取词有匹配,则对该词的权重乘以平滑处理的关键词权重
        keywords = jieba.analyse.extract_tags(self.query, topK=5, withWeight=True)
        for kw, weight in keywords:
            if kw in token_weight and kw not in self.stopwords:
                token_weight[kw] *= (1 + weight * 0.2)

        # 对一个字的分词，太容易干扰，降低权重为原来的1/3
        for word, weight in list(token_weight.items()):
            if len(word) == 1:
                token_weight[word] = weight / 3

        for word, weight in list(token_weight.items()):
            if word not in self.synonyms:
                continue
            for synonym_word in self.synonyms[word]:
                if synonym_word in self.stopwords:
                    continue
                if synonym_word in token_weight:
                    token_weight[synonym_word] += weight * 0.1
                else:
                    token_weight[synonym_word] = weight * 0.1

        token_weight = {k: round(v, 2) for k, v in token_weight.items() if round(v, 2) >= threshold}
        token_weight = dict(sorted(token_weight.items(), key=lambda item: item[1]))
        logger.info(self.log_prefix + f"问句关键词权重: {json.dumps(token_weight, ensure_ascii=False)}")

        return token_weight

    @staticmethod
    async def es_tokens(query: str, analyzer: str, index: str = "repo_0"):
        ik_smart_response = await es.indices.analyze(
            index=index,
            body={
                "analyzer": analyzer,
                "text": query,
            },
        )
        tokens = [token["token"] for token in ik_smart_response["tokens"]]

        return tokens


    async def get_query_token_weight_with_rewrite(self, threshold=0.4):
        """
        当有rewrite engine时，原始基于词库、jieba抽取关键词的方法不如扩展问句和关键词，因此需要优化方法

        Returns:

        """
        # 从扩展问句里获取es关键词
        token_weight = {}
        for query in self.query_search_terms:
            smart_tokens = await self.es_tokens(query=query, analyzer="ik_smart")
            # 每有一个重复的词,说明此词可能比较重要,权重加0.3
            for token in smart_tokens:
                if token in token_weight:
                    token_weight[token] += 0.3
                else:
                    token_weight[token] = 1

        # 反而可能是未扩展的改写问题一般会比较弱,但保留一半权重进行兜底
        smart_tokens = await self.es_tokens(query=self.query_rewrite, analyzer="ik_smart")
        for token in smart_tokens:
            if token in token_weight:
                token_weight[token] += 0.15
            else:
                token_weight[token] = 0.3

        # 添加问题关键词
        for keyword in self.query_keywords:
            if keyword in token_weight:
                token_weight[keyword] += 2
            else:
                token_weight[keyword] = 2

        # 添加问题联想词
        for keyword in self.query_associative_keywords:
            if keyword in token_weight:
                token_weight[keyword] += 2
            else:
                token_weight[keyword] = 2

        token_weight = {k: round(v, 2) for k, v in token_weight.items() if v >= threshold}
        token_weight = dict(sorted(token_weight.items(), key=lambda item: item[1]))
        logger.info(self.log_prefix + f"问句关键词权重: {json.dumps(token_weight, ensure_ascii=False)}")

        return token_weight

    def match_doc_query_tokens(self):
        """
        [文档筛选策略]根据问句、关键词、联想词等切分出的词和权重,以bm25匹配文档的策略
        Args:

        Returns:

        """
        if len(self.query_token_weight) >= 3:
            keyword_condition = [{
                "term": {
                    field: {
                        "value": t,
                        "boost": round(b * ratio, 1)
                    }
                }
            } for t, b in self.query_token_weight.items() for field, ratio in self.field_boost.items()]
        else:
            keyword_condition = [{
                "multi_match": {
                    "query": self.query_rewrite or self.query,
                    "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                    "type": "best_fields",
                    "tie_breaker": 0.7
                }
            }]
        return keyword_condition

    def match_doc_query_keyword(self):
        """
        [文档筛选策略]根据大模型推理的问句关键词,BM25匹配最相关文档

        Returns:

        """
        if self.query_keywords is None:
            return []

        keyword_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        "type": "best_fields",
                        "tie_breaker": 0.7
                    }
                } for kw in self.query_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        # "type": "phrase",
                        "analyzer": "ik_max_word",
                        "tie_breaker": 1.0
                    }
                } for kw in self.query_keywords
            ],
        ]

        return keyword_condition

    def match_doc_query_associative_keyword(self):
        """
        [文档筛选策略]根据大模型推理的问题联想词,BM25匹配最相关文档

        Returns:

        """
        if self.query_associative_keywords is None:
            return []

        associative_keyword_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        "type": "best_fields",
                        "tie_breaker": 0.7
                    }
                } for kw in self.query_associative_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.field_boost.items()],
                        # "type": "phrase",
                        "analyzer": "ik_max_word",
                        "tie_breaker": 1.0
                    }
                } for kw in self.query_associative_keywords
            ],
        ]
        return associative_keyword_condition

    def function_score_doc_multiply(self, power: float = 1.8):
        """
        [文档筛选策略]根据问句切分出的词和权重,对权重>0.5的词，在命中时累加权重，以实现更多的词命中，而非单一词命中足够多的目的
        Args:
            power:

        Returns:

        """
        multiply_tokens = [word for word, weight in self.query_token_weight.items() if weight > 0.5]

        return [
            {
                "filter": {
                    "term": {f"plain_text": tk}
                },
                "weight": power
            } for tk in multiply_tokens
        ]

    def function_score_doc_custom(self):
        return [boost for boost in self.custom_boost if "chunks." not in str(boost)] if self.custom_boost else []

    def match_chunk_keyword(self):
        """
        [分片筛选策略] 根据问句、关键词、联想词等切分出的词和权重,以bm25匹配文档的策略
        Returns:

        """
        if self.query_token_weight and len(self.query_token_weight) >= 3:
            keyword_condition = [{
                "term": {
                    field: {
                        "value": t,
                        "boost": round(b * ratio * self.bm25_weight, 2)
                    }
                }
            } for t, b in self.query_token_weight.items() for field, ratio in self.chunk_field_boost.items()]
        else:
            keyword_condition = [{
                "multi_match": {
                    "query": self.query,
                    "fields": [f"{chunk_field}^{ratio}" for chunk_field, ratio in self.chunk_field_boost.items()],
                    "type": "best_fields",
                    "tie_breaker": 0.7,
                    "boost": self.bm25_weight
                }
            }]

        return keyword_condition

    def match_chunk_query_keyword(self):
        """
        [分片筛选策略] 根据大模型提取的关键词,以bm25匹配段落的策略

        Returns:

        """
        if self.query_keywords is None:
            return []

        keyword_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.chunk_field_boost.items()],
                        "type": "best_fields",
                        "tie_breaker": 0.7
                    }
                } for kw in self.query_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "fields": [f"{field}^{ratio}" for field, ratio in self.chunk_field_boost.items()],
                        "type": "phrase",
                        "analyzer": "ik_max_word",
                        "tie_breaker": 1.0
                    }
                } for kw in self.query_keywords
            ]
        ]

        return keyword_condition

    def match_chunk_associative_keyword(self):
        """
        [分片筛选策略] 根据大模型提取的联想词，以bm25匹配段落的策略
        Returns:

        """
        if self.query_associative_keywords is None:
            return []

        associated_condition = [
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "type": "best_fields",
                        "fields": [f"{chunk_field}^{ratio}" for chunk_field, ratio in self.chunk_field_boost.items()],
                        "boost": self.bm25_weight
                    }
                } for kw in self.query_associative_keywords
            ],
            *[
                {
                    "multi_match": {
                        "query": kw,
                        "type": "phrase",
                        "fields": [f"{chunk_field}^{ratio}" for chunk_field, ratio in self.chunk_field_boost.items()],
                        "analyzer": "ik_max_word",
                        "boost": self.bm25_weight
                    }
                } for kw in self.query_associative_keywords
            ],
        ]
        return associated_condition

    def function_score_chunk_boost(self, doc_scores: dict, _min: (int, float) = 1, _max: (int, float) = 1.2):
        """压缩分数到[_min, _max]"""
        if not doc_scores:
            return []
        max_value = max(doc_scores.values())
        min_value = min(doc_scores.values())

        normalized_doc_scores = {
            doc_id: round(1 + (score - min_value) * (_max - _min) / (max_value - min_value), 3)
            if max_value > min_value
            else 1
            for doc_id, score in doc_scores.items()
        }
        logger.info(self.log_prefix + f"normalized_doc_scores: {normalized_doc_scores}")
        function_score_normalized = [
            {
                "filter": {
                    "prefix": {f"chunks.cid": f"{k}_"}
                },
                "weight": v
            } for k, v in normalized_doc_scores.items()
        ]
        return function_score_normalized

    def function_score_chunk_multiply(self) -> list[dict] | None:

        multiply_tokens = [word for word, weight in self.query_token_weight.items() if weight > 0.2]
        power = round(math.pow(self._es_max_score, 1 / len(self.query_token_weight)), 2)
        if power > 1.5:
            power = 1.5
        if power < 1.1:
            power = 1.1

        return [
            {
                "filter": {
                    "term": {"chunks.plaintext": tk}
                },
                "weight": power
            } for tk in multiply_tokens
        ]

    def function_score_chunk_custom(self):
        return [boost for boost in self.custom_boost if "chunks." in str(boost)] if self.custom_boost else []

    @staticmethod
    def post_process_doc_annealing(hits: list):
        """对同一个文档的多个区间进行退火"""
        doc_score_annealing_factor = 0.96

        for i, sr in enumerate(hits):
            sr["_score"] = round(sr["_score"] * doc_score_annealing_factor ** i, 4)
