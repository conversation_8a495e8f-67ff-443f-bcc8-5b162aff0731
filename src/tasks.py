#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from engine.rdb import load_session_context
from engine.taskiq_task import broker, scheduler
from config import TZ
from task.stock_data import AkshareDataOffline
from task.summarizer import SummarizerOffline
from task.analytics import AnalyticsOffline
from task.doc_parse import DocParseOffline
from task.extract import DocExtractOffline


@broker.task(task_name="doc_parsing")
@load_session_context
async def doc_parsing(doc_id: int):
    success = await DocParseOffline.parsing(doc_id=doc_id)
    if success:
        await doc_extract.kiq(doc_id=doc_id)

@broker.task(task_name="doc_extract")
@load_session_context
async def doc_extract(doc_id: int):
    await DocExtractOffline.extract(doc_id=doc_id)

@broker.task(task_name="summarizer_abstract")
@load_session_context
async def summarizer_abstract(summarizer_id: int):
    await SummarizerOffline.abstract(summarizer_id=summarizer_id)


@broker.task(task_name="dashboard_analyze")
@load_session_context
async def dashboard_analyze(dashboard_id: int):
    await AnalyticsOffline.analyze(dashboard_id=dashboard_id)


# @broker.task(task_name="get_stock_news", schedule=[{"cron": "*/5 * * * *"}])
# @load_session_context
# async def get_stock_news():
#     await AkshareDataOffline.get_stock_news()
#
#
@broker.task(task_name="get_stock_main_news_cx", schedule=[{"cron": "*/5 * * * *"}])
@load_session_context
async def get_stock_main_news_cx():
    await AkshareDataOffline.get_stock_main_news_cx()
#
#
# @broker.task(task_name="get_stock_info_global_ths", schedule=[{"cron": "*/5 * * * *"}])
# @load_session_context
# async def get_stock_info_global_ths():
#     await AkshareDataOffline.get_stock_info_global_ths()
#
#
# @broker.task(task_name="get_stock_value_flows", schedule=[{"cron": "5 16 * * *"}], cron_offset=TZ.zone)
# @load_session_context
# async def get_stock_value_flows():
#     await AkshareDataOffline.get_stock_value_flow()
#
#
# @broker.task(task_name="get_stock_institute_hold", schedule=[{"cron": "5 16 * * *"}], cron_offset=TZ.zone)
# @load_session_context
# async def get_stock_institute_hold():
#     await AkshareDataOffline.get_stock_institute_hold()
#
#
# @broker.task(task_name="get_stock_company_news", schedule=[{"cron": "*/5 * * * *"}])
# @load_session_context
# async def get_stock_company_news():
#     await AkshareDataOffline.get_stock_company_news()
#
#
# @broker.task(task_name="get_stock_score", schedule=[{"5 16 * * *"}], cron_offset=TZ.zone)
# @load_session_context
# async def get_stock_score():
#     await AkshareDataOffline.get_stock_score()
#
#
# @broker.task(task_name="get_stock_zh_a_hist", schedule=[{"cron": "*/5 * * * *"}])
# @load_session_context
# async def get_stock_zh_a_hist():
#     await AkshareDataOffline.get_stock_zh_a_hist()

# @chen: analyze的jsonout case
if __name__ == '__main__':
    import asyncio
    asyncio.run(dashboard_analyze(dashboard_id=20))

# # @chen: extract的tags错误 case
# if __name__ == "__main__":
#     import asyncio
#     asyncio.run(doc_extract(2243))